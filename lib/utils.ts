import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const formatDate = (inputDate: any, format: any) => {
  const date = new Date(inputDate);

  // Check if the date is valid
  if (isNaN(date.getTime())) return "";

  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const yearFull = date.getFullYear();
  const yearShort = yearFull.toString().slice(-2);

  format = format
    .replace("DD", day)
    .replace("MM", month)
    .replace("YYYY", yearFull)
    .replace("YY", yearShort);

  return format;
};

export const replacePlaceholders = (
  template: string,
  values: Record<string, string>
) => {
  return template?.replace(/{{(.*?)}}/g, (_, key) => values[key] || "");
};

/**
 * Utility function to preserve query parameters when navigating between routes
 * @param basePath - The base path to navigate to
 * @param currentSearchParams - Current URLSearchParams object or object with get method
 * @param preserveParams - Array of parameter names to preserve (defaults to ['opportunityId'])
 * @returns Complete URL with preserved parameters
 */
export const preserveQueryParams = (
  basePath: string,
  currentSearchParams: { get: (key: string) => string | null },
  preserveParams: string[] = ["opportunityId"]
): string => {
  const newParams = new URLSearchParams();

  // Preserve specified parameters if they exist
  preserveParams.forEach((param) => {
    const value = currentSearchParams.get(param);
    if (value) {
      newParams.set(param, value);
    }
  });

  // Return path with preserved parameters
  const queryString = newParams.toString();
  return queryString ? `${basePath}?${queryString}` : basePath;
};

/**
 * Extract opportunityId from search params with graceful fallback
 * @param searchParams - URLSearchParams object or object with get method
 * @returns opportunityId string or null
 */
export const extractOpportunityId = (searchParams: {
  get: (key: string) => string | null;
}): string | "" => {
  return searchParams.get("opportunityId") || "";
};
