import { atomWithStorage } from "jotai/utils";

export const nextForm = atomWithStorage("next-form", {});
export const email = atomWithStorage("email", "");
export const applicationId = atomWithStorage("applicationId", "");
export const routes = atomWithStorage("routes", "");
export const brandLogo = atomWithStorage("brandLogo", "");
export const programmeName = atomWithStorage("programmeName", "");
export const consumerAPIKey = atomWithStorage("consumerAPIKey", "");
export const preferredDateFormat = atomWithStorage("preferredDateFormat", "");
export const qualifyingQuestions = atomWithStorage("qualifying-questions", {});
export const selectedCountry = atomWithStorage("selectedCountry", "");
export const dateReplacement = atomWithStorage("dateReplacement", "");
export const preferredLanguage = atomWithStorage("preferredLanguage", "");
export const staticContentsAtom = atomWithStorage("staticContents", {});
export const requestTypeDataAtom = atomWithStorage(
  "requestTypeData",
  {} as any
);
export const fontSizeAtom = atomWithStorage<any>("fontSize", {});
