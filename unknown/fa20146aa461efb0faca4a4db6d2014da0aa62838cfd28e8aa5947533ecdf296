"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { FC, useEffect, useState } from "react";
import { consumerAPIKey, email, nextForm, qualifyingQuestions, applicationId } from "@/lib/atom";
import { useAtom } from "jotai";
import Modal from "react-modal";
import DynamicFields from "./DynamicFields";
import { Separator } from "../ui/separator";
import { cn } from "@/lib/utils";
import { Label } from "../ui/label";
import Image from "next/image";
import { useForm } from "react-hook-form";
import { useQuery } from "@tanstack/react-query";
import { getOapDetail, getOapFormSections, saveOapForm } from "@/api/api";
import { useRouter } from "next/navigation";
import loader from '../../public/loader.svg'
import { FormLayout } from "./formLayout";



interface Props {
    isOpen: boolean,
    setShowPrequalifyingModal: any,
    fieldData: any,
    questionariesSectionQuery: any,
    basicDetails: any,
    sectionQuery: any,
    labels: any
}


const PrequalifyingQuestionnairePopup: FC<Props> = ({ isOpen, fieldData, setShowPrequalifyingModal, labels, sectionQuery }) => {

    useEffect(() => {
        localStorage.getItem('basic-details,')
    }, [])

    const methods = useForm();
    const [pageDetails, setPageDetails] = useState({
        screen: process.env.NEXT_PUBLIC_OAP_NAME,
        mode: process.env.NEXT_PUBLIC_OAP_MODE,
    });
    const {
        handleSubmit,
        formState: { errors },
        register,
        watch,
        trigger,
        setError,
        clearErrors,
        getValues
    } = methods;

    const [apiKey] = useAtom(consumerAPIKey);
    const [nextFormDetails, setNextFormDetails]: any = useAtom(nextForm);
    const [userEmail, setUserEmail] = useAtom(email);
    const [currentQuestionIndex, setCurrentQuestionIndex] = useState<number>(0);
    const [applicationFields, setApplicationFields] = useState<any>({});
    const [isFilteredField, setIsFilteredField] = useState<boolean>(false);
    const [selectedAnswers, setSelectedAnswers] = useState<Record<string, string>>({});
    const [qualifyingQuestionsState, setQualifyingQuestionsState] = useAtom(qualifyingQuestions);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const router = useRouter();
    const [isComponentLoading, setIsComponentLoading] = useState(true);
    const [application, setApplication] = useAtom(applicationId);

    const handleValueChanged = async (selectedValue: any, item: any) => {
        const answer = selectedValue.value || selectedValue; // Normalize Yes/No values
        clearErrors(item.fieldName);
        setQualifyingQuestionsState((prev) => {
            return {
                ...prev,
                [item.fieldName]: answer,
            };
        });

        setApplicationFields((prev: any) => ({
            ...prev,
            [item.fieldName]: answer,
            // [`${item.fieldName}_isQualified`]: item.endUpWhen === answer ? "Unqualified" : "Qualified",
        }));
    };


    const {
        data: formQuery,
        refetch: refetchForm,
        isFetching: formQueryIsFetching,
    } = useQuery({
        // next sections page
        queryKey: [`test`],
        queryFn: async () => {
            if (!(sectionQuery?.section?.[0]?.name && sectionQuery?.form)) return;
            let res = await getOapFormSections(
                {
                    oap: nextFormDetails?.oap,
                    mode: nextFormDetails?.mode,
                    formName: sectionQuery?.form,
                    sectionName: sectionQuery?.section?.[0]?.name,
                },
                apiKey
            );

            return res;
        },
        enabled: !!(nextFormDetails && sectionQuery),
    });

    const { data: pageQuery, isFetching: isPageFetching } = useQuery({
        queryKey: [`${pageDetails.screen}-${pageDetails.mode}`],
        queryFn: async () => {
            let res = await getOapDetail(
                {
                    name: pageDetails.screen,
                    mode: pageDetails.mode,
                },
                apiKey
            );
            return res;
        },
        enabled: true,
    });

    const handleNext = async () => {
        const currentField = fieldData[currentQuestionIndex];
        const fieldName = currentField.fieldName;
        const fieldValue = applicationFields[fieldName];

        if (currentField.isMandatory === "true" && !fieldValue) {
            setError(fieldName, { message: currentField.rules?.message || "This field is required" });
            return;
        }
        clearErrors(fieldName);
        if (currentQuestionIndex < fieldData.length - 1) {
            setCurrentQuestionIndex(currentQuestionIndex + 1);
        }
    };

    const handleBack = () => {
        if (currentQuestionIndex > 0) {
            setCurrentQuestionIndex(currentQuestionIndex - 1);
        }
    };

    function flattenPayload(payload: any) {
        const flattened: any = {};
        for (const key in payload) {
            if (Object.prototype.hasOwnProperty.call(payload, key)) {
                if (typeof payload[key] === "object" && payload[key] !== null) {
                    flattened[key] = payload[key].value;
                } else {
                    flattened[key] = payload[key];
                }
            }
        }
        return flattened;
    }


    const handleSubmitTemp = async () => {

        try {
            const currentField = fieldData[currentQuestionIndex];
            const fieldName = currentField.fieldName;
            const fieldValue = applicationFields[fieldName];

            if (currentField.isMandatory === "true" && !fieldValue) {
                setError(fieldName, { message: currentField.rules?.message || "This field is required" });
                return;
            }
            clearErrors(fieldName); // Moved inside try block


            let temp = flattenPayload(applicationFields);
            setIsLoading(true);
            const basicDetailsFromStorage = JSON.parse(localStorage.getItem('basic-details') || '{}');


            let res = await saveOapForm(
                {
                    email: userEmail,
                    applicationId: application,
                    applicationStatus: "inProgress",
                    ...temp,
                    ...basicDetailsFromStorage,
                    ...qualifyingQuestionsState,
                    sectionLabel: formQuery?.label,
                },
                { oapName: nextFormDetails?.oap, mode: nextFormDetails?.mode },
                apiKey
            );

            setShowPrequalifyingModal(false);
            router.push("/application-filter");
            setIsLoading(false);
        } catch (error) {
            console.error("Error in handleSubmitTemp:", error);
            setIsLoading(false);
            return;
        }
    };

    useEffect(() => {
        if (!isPageFetching) {
            setTimeout(() => setIsComponentLoading(false), 500);
        }
    }, [isPageFetching]);

    if (isComponentLoading) {
        return (
            <main className="min-h-screen bg-on-background flex flex-col items-center justify-center overflow-hidden w-full">
                <Image
                    priority
                    src={loader}
                    height={32}
                    width={32}
                    alt="Loading..."
                />
            </main>
        )
    }
    return (
        <>
            {isOpen &&
                (<FormLayout pageQuery={pageQuery}>
                    <div
                        className="flex flex-col  gap-x-20  rounded-[24px]">
                        <div className="flex flex-col items-center flex-1 w-[90%] sm:w-[480px]">
                            <form>
                                <div className="bg-[#F4F4F4]  p-10  rounded-lg  shadow-xl flex-col space-y-10">
                                    <div className="space-y-3">
                                        <p className="text-3xl text-[#01B0E1] font-semibold flex justify-center">
                                            Qualifying Questions
                                        </p>
                                        <p className="text-md text-center">
                                            Please, answer some questions before registration
                                        </p>
                                    </div>

                                    <div className="flex gap-2">
                                        {[...Array(fieldData.length)].map((_, i) => (
                                            <Separator
                                                key={i}
                                                className={cn(
                                                    `w-[30%] h-1 rounded-md ${currentQuestionIndex >= i
                                                        ? "bg-[#01B0E1]"
                                                        : "bg-slate-300"
                                                    }`
                                                )}
                                            />
                                        ))}
                                    </div>

                                    <div className="bg-white p-3 rounded-lg">
                                        <div className="mb-3">
                                            <Label className="text-md font-semibold">
                                                {fieldData[currentQuestionIndex]?.displayName}
                                                {fieldData[currentQuestionIndex]?.isMandatory && (
                                                    <Label className="text-sm ml-1 text-red-500">*</Label>
                                                )}
                                            </Label>
                                        </div>

                                        <DynamicFields
                                            arrayIndex={currentQuestionIndex}
                                            watch={watch}
                                            // getLookupData={currentQuestion?.pickListValues || []}
                                            key={fieldData[currentQuestionIndex]?.fieldName}
                                            register={register}
                                            fieldItem={fieldData[currentQuestionIndex]} // current question
                                            fieldType={fieldData[currentQuestionIndex]?.type}
                                            fieldName={fieldData[currentQuestionIndex]?.fieldName}
                                            isVisibleWhen={fieldData[currentQuestionIndex]?.visibleWhen}
                                            trigger={trigger}
                                            fromApplicationFilter={true}
                                            selectedValue={
                                                fieldData[currentQuestionIndex]?.type === "pickList" ||
                                                    fieldData[currentQuestionIndex]?.type === "dropDown"
                                                    ? applicationFields[fieldData[currentQuestionIndex].fieldDisplayName]
                                                    : applicationFields[fieldData[currentQuestionIndex]?.fieldName]
                                            } // yes or no
                                            handleValueChanged={(value: any) =>
                                                handleValueChanged(value, fieldData[currentQuestionIndex])
                                            }
                                            errorMessage={errors[fieldData[currentQuestionIndex]?.fieldName]?.message}
                                            isFilteredField={isFilteredField}
                                            selectedAnswers={selectedAnswers}
                                        />
                                    </div>
                                    <div className="mt-6 flex justify-end gap-x-3">
                                        {currentQuestionIndex > 0 && (
                                            <Button
                                                type="button"
                                                onClick={handleBack}
                                                className="bg-[#E8E6E5] hover:bg-[#C8C6C5] text-md font-bold px-6 py-6 w-full rounded"
                                            >
                                                ← Back
                                            </Button>
                                        )}
                                        <Button
                                            type="button"
                                            onClick={
                                                currentQuestionIndex === fieldData.length - 1 ? handleSubmitTemp : handleNext
                                            }
                                            className="bg-[#015687] hover:bg-[#00304B] text-white text-md text-lg font-bold px-6 py-6 w-full rounded"
                                        >
                                            {currentQuestionIndex === fieldData.length - 1 ?
                                                !isLoading ?
                                                    "Submit" : (
                                                        <Image
                                                            priority
                                                            src={loader}
                                                            height={32}
                                                            width={32}
                                                            alt="Loading..."
                                                        />
                                                    ) :
                                                "Next"}
                                        </Button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </FormLayout>
                )
            }

        </>
    );
};

export default PrequalifyingQuestionnairePopup;

