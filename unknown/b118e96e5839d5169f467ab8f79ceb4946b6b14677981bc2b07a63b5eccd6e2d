import { FC, useState } from "react";
import { cn } from "@/lib/utils";
import Image from "next/image";

interface Props {
    options: any,
    required: boolean,
    displayName: string,
    formQuery: any,
    imgSrc: any,
    handleSelect: (value: any) => void;
}


const CountrySelector: FC<Props> = ({ options = [], displayName, handleSelect, imgSrc }) => {
    return ( 
        <div className="p-6 mt-24 md:mt-28 lg:mt-36 flex flex-col items-center justify-center sm:w-full lg:w-[80%] md:flex-row md:items-start gap-5">
            <div className="text-2xl font-bold sm:w-full lg:w-[39%]">{displayName}</div>
            <div className="flex flex-col w-[100%] lg:w-[39%] text-gray-800 gap-5">
                {options?.map((option: any, index:number) => (
                    <div
                        key={option.value}
                        className="
                            bg-white rounded-md sm:w-[90%]  px-[40px] pb-[70px] pt-[20px] relative flex gap-4 cursor-pointer md:w-[450px] font-extrabold"
                        onClick={() => handleSelect(option?.value)}
                    >
                        <div className="font-extrabold w-full  text-2xl hover:text-[#F78B85]">{option.displayText || option.label}</div>
                        <div className="absolute right-0 bottom-0">
                        <img
                            src={imgSrc[index]}
                            alt="image"
                            width={210}
                            height={210}
                            className="object-cover"
                        />
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default CountrySelector;