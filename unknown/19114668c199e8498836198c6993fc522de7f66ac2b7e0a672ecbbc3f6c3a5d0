'use client';
import { selectedCountry } from '@/lib/atom';
import { useAtom } from 'jotai';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

interface CountryCardProps {
    country: string;
    imageSrc: string;
}

export default function CountryCard({ country, imageSrc }: CountryCardProps) {
    const router = useRouter();
    const [_,setSelectedCountry] = useAtom(selectedCountry);
    const handleClick = () => {
        
        setSelectedCountry(country);
        router.push(`/application-filter`);
    }
    return (
        <div onClick={handleClick} className="bg-white rounded-md px-[40px] py-[30px] relative flex gap-4 cursor-pointer w-[400px]">
            <div className="font-extrabold text-2xl hover:text-[#F78B85]">{country}</div>
            <div className="absolute right-0 bottom-0">
            <Image 
                src={imageSrc}
                alt={`${country}`} 
                width={180} 
                height={100}
                className="object-cover opacity-70" 
            />
        </div>
        </div>
    );
}
