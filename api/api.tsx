interface RequestOptions {
  method?: string;
  headers: any;
  body: any;
}
export const apiCall = async (url: string, options: any, queryParams?: any) => {
  try {
    const apiUrl = process.env.NEXT_PUBLIC_OAP_BACKEND_URL;
    const queryString = new URLSearchParams(queryParams).toString();

    let apiUrlWithParams = `${apiUrl}/${url}`;

    if (queryString) {
      if (apiUrlWithParams.includes("?")) {
        apiUrlWithParams += `&${queryString}`;
      } else {
        apiUrlWithParams += `?${queryString}`;
      }
    }

    const requestOptions: RequestOptions = {
      method: options?.method,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(options?.payload),
    };
    if (options?.key) {
      requestOptions.headers["x-api-key"] = options?.key;
    }

    const response = await fetch(apiUrlWithParams, {
      ...requestOptions,
      cache: "no-store",
    });

    try {
      const data = await response.json();
      if (data) {
        return data;
      }
    } catch (error) {
      return response;
    }
  } catch (error) {
    throw error;
  }
};

export const getLookUpData = async (
  payload: any,
  key: string,
  queryParams?: any
) => {
  const routeName = `${payload?.name}`;
  return await apiCall(
    routeName,
    {
      method: "GET",
      key,
    },
    queryParams
  );
};

export const getOapDetail = async (queryParams: any, key?: string) => {
  return apiCall("oap", { method: "GET", key }, queryParams);
};

export const getOapForm = async (queryParams: any, key: string) => {
  return apiCall("oap/forms", { method: "GET", key }, queryParams);
};

export const getOapFormSections = async (queryParams: any, key: string) => {
  return apiCall("oap/form/sections", { method: "GET", key }, queryParams);
};

export const getStudentDetailsById = async (queryParams: any, key: string) => {
  return apiCall(
    "oap/getstudentdetailsbyid",
    { method: "GET", key },
    queryParams
  );
};

export const getStudentDetails = async (
  oap: string,
  email: string,
  applicationId: string,
  key: string
) => {
  return apiCall(
    `oap/getstudentdetails`,
    { method: "GET", key },
    {
      oapName: oap,
      email,
      applicationId,
    }
  );
};
export const saveOapForm = async (
  payload: any,
  queryParams: any,
  key: string
) => {
  return apiCall(
    `oap/savestudentdetails`,
    { method: "POST", payload, key },
    queryParams
  );
};

export const getPreSignedURL = async (payload: any, key: string) => {
  return apiCall(`oap/uploadstudentdocument/getsignedurl`, {
    method: "POST",
    payload,
    key,
  });
};

export const getDocumentName = async (queryParams: any, key: string) => {
  return apiCall(`oap/getstudentdocument`, { method: "GET", key }, queryParams);
};

export const deleteDocument = async (queryParams: any, key: string) => {
  return apiCall(
    `oap/deletestudentdocument`,
    { method: "DELETE", key },
    queryParams
  );
};

export const getUpdatedDoc = async (payload: any, key: string) => {
  return apiCall(`oap/uploadstudentdocument`, { method: "POST", payload, key });
};

export const getAllSectionForms = async (queryParams: any, key?: string) => {
  return apiCall(`oap/sections`, { method: "GET", key }, queryParams);
};

export const getOpportunityDetails = async (
  opportunityId: string,
  key?: string,
  queryParams?: any
) => {
  return apiCall(
    `oap/opportunity/${opportunityId}`,
    { method: "GET", key },
    queryParams
  );
};

export const submitChangeRequest = async (
  payload: any,
  queryParams: any,
  key: string
) => {
  return apiCall(
    `oap/submitchangerequest`,
    { method: "POST", payload, key },
    queryParams
  );
};