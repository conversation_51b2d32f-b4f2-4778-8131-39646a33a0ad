import React from "react";
import {
  <PERSON>ert<PERSON><PERSON>og,
  AlertDialog<PERSON>ction,
  AlertDialogContent,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Check, Copy } from "lucide-react";
import { Button } from "@/components/ui/button";
import toast from "react-hot-toast";

interface DeferralSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  onReturnToDeferral: () => void;
  successMessages: any;
}

const DeferralSuccessModal: React.FC<DeferralSuccessModalProps> = ({
  isOpen,
  onClose,
  onReturnToDeferral,
  successMessages,
}) => {
  const [copied, setCopied] = React.useState(false);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  if (!isOpen) return null;

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="max-w-lg border-0 shadow-lg">
        <div className="bg-primary h-2 w-full absolute top-0 left-0 rounded-t-lg"></div>
        <AlertDialogHeader className="flex flex-col items-center pt-6">
          <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-4">
            <Check className="h-8 w-8 text-green-600" />
          </div>
          <AlertDialogTitle className="text-xl font-semibold text-center text-gray-800">
            {successMessages.title}
          </AlertDialogTitle>
          <div className="mt-4 text-center space-y-4">
            <p className="text-gray-600">{successMessages.description}</p>
          </div>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-4 mt-2">
          <AlertDialogAction
            onClick={onReturnToDeferral}
            className="bg-primary hover:bg-primary/90 text-white font-medium w-full order-1 sm:order-2"
          >
            {successMessages.buttonText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeferralSuccessModal;
