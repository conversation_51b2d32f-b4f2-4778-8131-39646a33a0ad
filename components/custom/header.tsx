import { Label } from "@radix-ui/react-label";
import Image from "next/image";
 import { useAtom } from "jotai";
 import { fontSizeAtom } from "@/lib/atom";
 import { getBrandSpecificFontStyle } from "@/lib/brandUtils";

export default function Header({
  title,
  logo,
  image,
  sectionQueryIsFetching,
  logoInfo,
}: any) {
   const [fontSize] = useAtom(fontSizeAtom);
  return (
    <>
      <div className="md:px-20 items-center h-20 w-full bg-white shadow-lg flex-row px-5 hidden lg:flex md:flex">
        <img src={logo} className=" h-8 w-8" height={32} width={32} />
        <Label className=" mx-3 text-base font-medium " style={getBrandSpecificFontStyle(fontSize, "header-title")}> {title}</Label>
      </div>

      <div className="md:px-20 items-center  h-20 w-full bg-on-background shadow-lg flex flex-row px-5 sm:flex md:hidden lg:hidden xl:hidden">
        {sectionQueryIsFetching ? (
          <></>
        ) : (
          <Image
            priority
            src={image}
            height={logoInfo?.height ?? 28}
            width={logoInfo?.width ?? 60}
            style={{ objectFit: "contain" }}
            alt="college_logo"
          />
        )}
      </div>
    </>
  );
}
