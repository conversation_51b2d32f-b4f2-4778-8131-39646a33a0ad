import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface AlertBoxProps {
  children?: React.ReactNode;
  title: string;
  placeholder: string;
}

export function AlertBox({ children, title, placeholder }: AlertBoxProps) {
  return (
    <AlertDialog defaultOpen>
      {/* <AlertDialogTrigger>Open</AlertDialogTrigger> // open button // */}
      <AlertDialogContent className="  max-w-6xl ">
        <AlertDialogHeader>
          <AlertDialogTitle className=" text-center border-border border-b pb-3 -mx-6">
            {title}
          </AlertDialogTitle>
          {children}
        </AlertDialogHeader>
        <AlertDialogFooter className=" flex flex-row justify-center sm:justify-center  ">
          <AlertDialogAction className=" text-text-secondary  sm:justify-center w-1/3  font-bold">
            {placeholder}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
