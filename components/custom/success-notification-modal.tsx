import React, { useEffect } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialog<PERSON>ontent,
  Alert<PERSON>ialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Check } from "lucide-react";
import DynamicField from "./DynamicFields";
import {  FormProvider, useFormContext } from "react-hook-form";

interface SuccessNotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  message?: string;
  userAlertMessage?: string;
  buttonText?: string;
  isNameSplit?: boolean;
  fieldItem?: any;
  fieldData?: any[];
}

const SuccessNotificationModal: React.FC<SuccessNotificationModalProps> = ({
  isOpen,
  onClose,
  title = "Success",
  message = "Your file has been successfully uploaded and processed.",
  buttonText = "OK",
  isNameSplit = false,
  fieldData,
}) => {
  const methods = useFormContext();
  const { register, setValue, watch, formState: { errors }, trigger, clearErrors, setError, getValues } = methods;

  // Prefill passportName with passportFirstName if needed
  useEffect(() => {
    if (isOpen) {
      const values = getValues();
      if (values.passportFirstName) {
        setValue('passportName', values.passportFirstName);
      }
    }
  }, [isOpen]);

  const handleOpenChange = (open: boolean) => {
    
    if (!open) {
      const formValues = getValues();
      let hasValidationErrors = false;
      
      const nameSplitFields = fieldData || [];
      
      for (let i = 0; i < nameSplitFields.length; i++) {
        const field = nameSplitFields[i];
        const fieldValue = formValues[field.fieldName];

        if (field.required && (!fieldValue || fieldValue.trim() === '')) {
          setError(field.fieldName, {
            type: "required",
            message: field.rules?.required
          });
          hasValidationErrors = true;
        }
        
        if (field.limit && fieldValue && fieldValue.length > field.limit) {
          hasValidationErrors = true;
          break;
        }
      }
      
      if (hasValidationErrors) {
            return; 
      }
      onClose();
    }
  };

  const handleSubmit = () => {
    if (isNameSplit) {
      const formValues = getValues();
      
      let hasValidationErrors = false;
      
      const nameSplitFields = fieldData || [];
      
      for (let i = 0; i < nameSplitFields.length; i++) {
        const field = nameSplitFields[i];
        const fieldValue = formValues[field.fieldName];
        if (field.required && (!fieldValue || fieldValue.trim() === '')) {
          setError(field.fieldName, {
            type: "required",
            message: field.rules?.required
          });
          hasValidationErrors = true;
        }
        
        if (field.limit && fieldValue && fieldValue.length > field.limit) {
          setError(field.fieldName, {
            type: "maxLength",
            message: field.errorMessage 
          });
          hasValidationErrors = true;
        }
      }
      
      if (hasValidationErrors) {
        return;
      }
      onClose();
    } else {
      onClose();
    }
  };

  if (!isOpen) return null;

  const nameSplitFields = fieldData || [];

  const dynamicButtonText = isNameSplit ? "Save and Proceed" : buttonText;

  return (
    <AlertDialog open={isOpen} onOpenChange={handleOpenChange}>
      <AlertDialogContent className="max-w-md border-0 shadow-lg">
        <div className="bg-primary h-2 w-full absolute top-0 left-0 rounded-t-lg"></div>
        <AlertDialogHeader className="flex flex-col items-center pt-6">
          <div className="w-16 h-16 rounded-full bg-red-200 flex items-center justify-center mb-4">
            <Check className="h-8 w-8 text-primary" />
          </div>
          <AlertDialogTitle className="text-xl font-semibold text-center text-gray-800">
            {title}
          </AlertDialogTitle>
          <div className="mt-2 text-center text-gray-600">{message}</div>
          {isNameSplit && (
            <FormProvider {...methods}>
              <div className="w-full mt-3 space-y-4">
                {nameSplitFields?.map((field: any, index: number) => {
                  return (
                    <DynamicField
                      key={index}
                      fieldItem={field}
                      register={register}
                      selectedValue={watch(field.fieldName) || ""}
                      isVisibleWhen={true}
                      label={field.displayName}
                      name={field.fieldName}
                      watch={watch}
                      setValue={setValue}
                      errorMessage={errors[field.fieldName]?.message}
                      handleValueChanged={(value: any, type?: string) => {
                        clearErrors(field.fieldName)
                        setValue(field.fieldName, value);
                       
                        if (type === "pickList" && field?.fieldDisplayName) {
                          setValue(field.fieldDisplayName, value);
                        }
                        if (field?.resetChild) {
                          setValue(field.resetChild, "");
                          clearErrors(field.resetChild);
                        }
                        if (field.subFieldName && Array.isArray(field.subFieldName)) {
                          field.subFieldName.forEach((subFieldName: string) => {
                            setValue(subFieldName, value);
                          });
                        }
                        else if (field.subFieldName) {
                          setValue(field.subFieldName, value);
                        }
                      }}
                      trigger={trigger}
                      clearErrors={clearErrors}
                      setError={setError}
                      disabled={false}
                    />
                  );
                })}
              </div>
            </FormProvider>
          )}
        </AlertDialogHeader>
        <AlertDialogFooter className="flex justify-center sm:justify-center mt-4">
          <AlertDialogAction
            onClick={handleSubmit}
            className="bg-primary hover:bg-success/90 text-white font-medium px-8 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {dynamicButtonText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default SuccessNotificationModal;
