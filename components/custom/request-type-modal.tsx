"use client";

import { FC, useState } from "react";
import { useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { useForm, FormProvider } from "react-hook-form";
import Modal from "react-modal";
import Image from "next/image";

import { getOapDetail } from "@/api/api";
import { consumerAPIKey } from "@/lib/atom";
import { RequestType } from "@/lib/requestTypes";
import { preserveQueryParams } from "@/lib/utils";
import { PopUpRadioButton } from "./radioButton";
import loader from "../../public/loader.svg";

interface RequestTypeModalProps {
  isOpen?: boolean;
  onClose?: () => void;
  opportunityId?: string | null;
  requestTypes?: RequestType[];
}

const RequestTypeModal: FC<RequestTypeModalProps> = ({
  isOpen = true,
  onClose,
  opportunityId,
  requestTypes,
}) => {
  const router = useRouter();
  const [apiKey] = useAtom(consumerAPIKey);
  const [selectedRequestType, setSelectedRequestType] =
    useState<RequestType | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const [pageDetails] = useState({
    screen: process.env.NEXT_PUBLIC_OAP_NAME,
    mode: process.env.NEXT_PUBLIC_OAP_MODE,
  });

  const methods = useForm();
  const {
    register,
    handleSubmit: onFormSubmit,
    setValue,
    watch,
    formState: { errors },
  } = methods;

  // Fetch page details using pageQuery pattern from applicationFilter
  const { data: pageQuery, isFetching: pageQueryFetching } = useQuery({
    queryKey: [`${pageDetails.screen}-${pageDetails.mode}`],
    queryFn: async () => {
      let res = await getOapDetail(
        {
          name: pageDetails.screen,
          mode: pageDetails.mode,
        },
        apiKey
      );
      return res;
    },
    enabled: !!apiKey,
  });

  // Transform requestTypes for dropdown
  const requestTypeOptions = requestTypes?.map((type) => ({
    label: type.name,
    value: type.formName,
    ...type,
  }));

  const handleRequestTypeChange = (selectedValue: any) => {
    const requestType = requestTypes?.find(
      (type) => type.formName === selectedValue
    );
    setSelectedRequestType(requestType || null);
    setValue("requestType", { value: selectedValue, label: requestType?.name });
  };

  const handleSubmit = async () => {
    if (!selectedRequestType) return;

    setIsLoading(true);
    try {
      // Create URLSearchParams to preserve opportunityId if present
      const searchParams = new URLSearchParams();
      if (opportunityId) {
        searchParams.set("opportunityId", opportunityId);
      }

      // Navigate to the dynamic route for the selected request type with preserved parameters
      const targetPath = preserveQueryParams(
        selectedRequestType.path,
        searchParams
      );
      router.push(targetPath, { scroll: false });
    } catch (error) {
      console.error("Error navigating to request form:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (onClose) {
      onClose();
    } else {
      router.back();
    }
  };

  if (pageQueryFetching) {
    return (
      <main className="min-h-screen bg-on-background flex flex-col items-center justify-center overflow-scroll w-full">
        <Image priority src={loader} height={32} width={32} alt="Loading..." />
      </main>
    );
  }

  return (
    <div className="min-h-screen">
      <Modal
        isOpen={isOpen}
        className="fixed inset-0 flex flex-col justify-start items-center outline-none pt-16 overflow-y-scroll"
        overlayClassName="fixed inset-0 bg-on-background"
      >
        <div className="flex flex-col items-center outline-none">
          <FormProvider {...methods}>
            <div>
              <Image
                className="header-brand-image"
                src={pageQuery?.logoInfo?.signedUrl}
                alt="university_logo"
                width={pageQuery?.logoInfo?.width}
                height={pageQuery?.logoInfo?.height}
                priority={true}
              />
            </div>
            <form onSubmit={onFormSubmit(handleSubmit)}>
              <div className="bg-background w-[480px] p-10 mt-12 rounded-lg shadow-xl flex-col space-y-8">
                <div className="space-y-3">
                  <p className="text-3xl font-semibold flex justify-center">
                    Change Request Form
                  </p>
                  <p className="text-md text-center">
                    Please select the type of change request form you would like
                    to submit
                  </p>
                </div>

                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium mb-4">
                      Select Change Request Form{" "}
                      <span className="text-error">*</span>
                    </label>
                    <PopUpRadioButton
                      id="requestType"
                      selectedValue={watch("requestType")?.value || ""}
                      handleChange={handleRequestTypeChange}
                      options={
                        requestTypeOptions?.map((type) => ({
                          value: type.value,
                          label: type.label,
                        })) || []
                      }
                      register={register("requestType", {
                        required: "Please select a request type",
                      })}
                      errorMessage={errors.requestType?.message as string}
                    />
                  </div>
                </div>

                <div className="flex justify-between space-x-4">
                  <button
                    type="button"
                    onClick={handleSubmit}
                    disabled={!selectedRequestType || isLoading}
                    className="button button_primary bg-secondary hover:bg-primary w-full mx-auto flex justify-center items-center text-white font-bold h-[41px] rounded cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <Image
                        src={loader}
                        height={20}
                        width={20}
                        alt="Loading..."
                      />
                    ) : (
                      "Continue"
                    )}
                  </button>
                </div>
              </div>
            </form>
          </FormProvider>
        </div>
      </Modal>
    </div>
  );
};

export default RequestTypeModal;
