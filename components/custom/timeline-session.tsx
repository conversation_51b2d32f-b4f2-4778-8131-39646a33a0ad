"use client";
import { <PERSON><PERSON><PERSON>ir<PERSON>, Check } from "lucide-react";
import { Label } from "../ui/label";
import { Progress } from "../ui/progress";
import { getOapForm } from "@/api/api";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import Image from "next/image";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useAtom } from "jotai";
import {
  nextForm,
  routes,
  consumerAPIKey,
  preferredLanguage,
  staticContentsAtom,
  fontSizeAtom,
} from "@/lib/atom";
import loader from "../../public/loader.svg";
import { useEffect } from "react";
import { useFormContext } from "react-hook-form";
import { getBrandSpecificFontStyle } from "@/lib/brandUtils";

interface TimelineSessionProps {
  applicationProgressDetail?: any;
  errors?: any;
  items?: any;
  progressValue: number;
  validationError?: any;
  getSideBarStatus?: any;
  handleFormSubmit?: any;
  allSections?: any;
  appId: string;
  triggerError: boolean;
  canDisableTabs: boolean;
  completedText?: any;
}

interface TimeLineComponentProps {
  index: number;
  isLastIndex: boolean;
  status?: any;
  slug?: string;
  title?: string;
  error?: any;
  triggerError: boolean;
  canDisableTabs: boolean;
  handleFormSubmit?: any;
  completedText?: any;
}

export default function TimelineSession({
  progressValue,
  validationError,
  getSideBarStatus,
  handleFormSubmit,
  allSections,
  appId,
  canDisableTabs,
  triggerError,
  completedText,
}: TimelineSessionProps) {
  // use timeline session to call api for section
  const searchParams = useSearchParams();
  const apply: any = searchParams.get("apply");
  const [apiKey] = useAtom(consumerAPIKey);
  const [preferLang, setPreferLang] = useAtom(preferredLanguage);

  const [nextFormDetails]: any = useAtom(nextForm);
  const [, setRoute] = useAtom(routes);
  const [staticContents] = useAtom<any>(staticContentsAtom);

  const {
    data: sectionQuery,
    isFetching: sectionQueryFetching,
    refetch: refetchSectionQuery,
  } = useQuery({
    queryKey: [
      `${nextFormDetails?.oap}-${nextFormDetails?.mode}-${nextFormDetails?.[apply]}-form1`,
    ],
    queryFn: async () => {
      if (nextFormDetails?.type == "single") {
        if (
          nextFormDetails?.form &&
          nextFormDetails?.oap &&
          nextFormDetails?.mode
        ) {
          let res = await getOapForm(
            {
              oap: nextFormDetails?.oap,
              form: nextFormDetails?.form,
              mode: nextFormDetails?.mode,
              ...(preferLang === "de" && { language: "de" }),
            },
            apiKey
          );
          return res;
        }
      } else {
        if (
          nextFormDetails?.[apply] &&
          nextFormDetails?.oap &&
          nextFormDetails?.mode
        ) {
          let res = await getOapForm(
            {
              oap: nextFormDetails?.oap,
              form: nextFormDetails?.[apply],
              mode: nextFormDetails?.mode,
              ...(preferLang === "de" && { language: "de" }),
            },
            apiKey
          );
          return res;
        } else {
          return;
        }
      }
    },
    enabled: !!nextFormDetails,
  });
  const items = sectionQuery?.section;

  useEffect(() => {
    if (allSections?.length) {
      setRoute(allSections);
    }
  }, [allSections]);
  return (
    <>
      {sectionQueryFetching ? (
        <div>
          <Image
            priority
            src={loader}
            height={32}
            width={32}
            style={{ objectFit: "contain" }}
            className="mb-5 sm:hidden md:flex lg:flex xl:flex"
            alt="loader"
          />
        </div>
      ) : (
        <Image
          priority
          src={sectionQuery?.logoInfo?.signedUrl}
          height={sectionQuery?.logoInfo?.height}
          width={sectionQuery?.logoInfo?.width}
          style={{ objectFit: "contain" }}
          className="mb-5 sm:hidden md:flex lg:flex xl:flex"
          alt="college_logo"
        />
      )}

      <>
        <div className=" flex  w-full justify-between">
          <div className="mb-1 text-text-secondary text-sm ">
            {staticContents?.application?.applicationId || "APPLICATION ID"} -
            <Label className="font-bold"> {appId}</Label>
          </div>
        </div>
        <Progress
          value={progressValue}
          className="bg-surface  border border-border"
        />
        <div className=" mt-2 ">
          <Label className="mb-1 text-text-secondary">{`${
            completedText?.replace("{progress}", progressValue) ||
            "0% Completed"
          }`}</Label>
        </div>
      </>

      <div className="lg:mt-5 xl:mt-5">
        {allSections?.map(
          (item: { displayName: any; slug: string }, index: number) => {
            return (
              <TimeLineComponent
                key={index}
                index={index}
                isLastIndex={allSections?.length === index + 1}
                title={item.displayName}
                slug={item.slug}
                error={validationError}
                status={getSideBarStatus(item?.displayName)}
                handleFormSubmit={handleFormSubmit}
                triggerError={triggerError}
                canDisableTabs={canDisableTabs}
              />
            );
          }
        )}
      </div>
    </>
  );
}

const TimeLineComponent = ({
  index,
  isLastIndex,
  status,
  title,
  slug,
  error,
  triggerError,
  canDisableTabs,
  handleFormSubmit,
}: TimeLineComponentProps) => {
  const router = useRouter();
  const searchParams: any = new URLSearchParams(window.location.search);
  const step = searchParams.get("step");
  searchParams.set("step", index);
  const updatedQueryString = searchParams.toString();
  const isActive = Number(step) === index;
  const [staticContent] = useAtom<any>(staticContentsAtom);
  const [fontSize] = useAtom(fontSizeAtom);
  return (
    <div className="flex flex-row" key={index}>
      <div className="flex relative pt-[10px]">
        <div className="pt-[3px]">
          <IconContainer status={status} slugName={slug} index={index} />
        </div>
        {!isLastIndex ? (
          <div
            className={`w-full ${
              status === "completed" ? "h-1/2" : "h-3/4"
            } border-text-secondary border-l absolute ${
              status === "completed" ? "top-[32px]" : "top-[23px]"
            } ml-[7px]`}
          />
        ) : null}
        <div
          className={`text-text-secondary pr-4 ${
            isActive ? "opacity-100" : "opacity-50"
          } ${
            canDisableTabs ? "cursor-not-allowed opacity-50" : "cursor-pointer"
          } flex flex-column pb-[10px] max-[80%] flex-wrap`}
          style={getBrandSpecificFontStyle(fontSize, "timeline-label")}
          onClick={() => {
            if (!isActive && !canDisableTabs) {
              handleFormSubmit(null);
              router.push(`/form?${updatedQueryString}`);
            }
          }}
        >
          {title}
        </div>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild className="cursor-pointer">
              {error?.some(
                (item: any) =>
                  item?.displayName === title && item?.missingFieldsExist
              ) &&
                triggerError && (
                  <div className="w-6 ">
                    <AlertCircle
                      name="shield-alert"
                      height={20}
                      width={20}
                      color={"var(--color-surface)"}
                    />
                  </div>
                )}
            </TooltipTrigger>
            <TooltipContent className="bg-background">
              <p>
                {staticContent?.errors?.application?.totalFieldsRequired?.replace(
                  "{fieldcount}",
                  error?.find((item: any) => item?.displayName === title)
                    ?.missingFieldsCount
                ) ||
                  `${
                    error?.find((item: any) => item?.displayName === title)
                      ?.missingFieldsCount
                  } fields  is required`}
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
};

interface IconContainerProps {
  status: string;
  slugName: string | any;
  index: number;
}

const IconContainer = ({ slugName, status, index }: IconContainerProps) => {
  const pathname = usePathname();
  const searchParams: any = new URLSearchParams(window.location.search);
  const currentRoute = pathname.replace("/", "");
  const step = searchParams.get("step");
  if (step == index) {
    // current section big dot
    return (
      <div className="w-6">
        <div className="h-4 w-4 bg-text-secondary rounded-full" />
      </div>
    );
  }
  if (currentRoute !== slugName && status !== "completed") {
    // not current section
    return (
      <div className="w-6">
        <div className="h-[10px] w-[10px] bg-text-secondary rounded-full opacity-50 ml-[2px]" />
      </div>
    );
  }
  if (status === "completed") {
    // tick mark completed
    return (
      <div className="w-6">
        <Check
          name="Check"
          height={20}
          width={20}
          color={"var(--color-text-secondary)"}
        />
      </div>
    );
  }
};
