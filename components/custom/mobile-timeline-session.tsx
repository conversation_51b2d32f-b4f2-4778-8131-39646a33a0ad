"use client";
import { <PERSON><PERSON>Circle, Check } from "lucide-react";
import { Label } from "../ui/label";
import { Progress } from "../ui/progress";
import { useRouter, usePathname } from "next/navigation";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { staticContentsAtom } from "@/lib/atom";
import { useAtom } from "jotai";

interface TimelineSessionProps {
  applicationProgressDetail?: any;
  errors?: any;
  items?: any;
  progressValue?: number;
  validationError?: any;
  getSideBarStatus?: any;
  setIsDialogOpen?: any;
  handleFormSubmit?: any;
  appId: string;
}

interface TimeLineComponentProps {
  index: number;
  isLastIndex: boolean;
  status?: any;
  slug?: string;
  title?: string;
  error?: any;
  setIsDialogOpen?: any;
  handleFormSubmit?: any;
}

export default function MobileTimelineSession({
  validationError,
  getSideBarStatus,
  items,
  progressValue,
  setIsDialogOpen,
  handleFormSubmit,
  appId,
}: TimelineSessionProps) {
  // use timeline session to call api for section

  const [staticContents] = useAtom<any>(staticContentsAtom);

  return (
    <>
      <>
        <div className=" flex  w-full justify-between">
          <div className="mb-1 text-text-secondary text-sm ">
            {staticContents?.application?.applicationId || "APPLICATION ID"} -
            <Label className="font-bold"> {appId}</Label>
          </div>
        </div>

        <Progress
          value={progressValue}
          className="bg-primary  border border-border"
        />

        <div className=" mt-2">
          <Label className="mb-1 text-text-secondary">{`${
            progressValue || 0
          }% Completed`}</Label>
        </div>
      </>

      <div className="lg:mt-5 xl:mt-5">
        {items?.map(
          (item: { displayName: any; slug: string }, index: number) => {
            return (
              <TimeLineComponent
                key={index}
                index={index}
                isLastIndex={items?.length === index + 1}
                title={item.displayName}
                slug={item.slug}
                error={validationError}
                setIsDialogOpen={setIsDialogOpen}
                status={getSideBarStatus(item?.displayName)}
                handleFormSubmit={handleFormSubmit}
              />
            );
          }
        )}
      </div>
    </>
  );
}

const TimeLineComponent = ({
  index,
  isLastIndex,
  status,
  title,
  slug,
  error,
  setIsDialogOpen,
  handleFormSubmit,
}: TimeLineComponentProps) => {
  const router = useRouter();
  const searchParams: any = new URLSearchParams(window.location.search);
  const step = searchParams.get("step");
  searchParams.set("step", index);
  const updatedQueryString = searchParams.toString();
  const isActive = Number(step) === index;

  return (
    <div className="flex flex-row" key={index}>
      <div className="flex relative pt-[10px]">
        <div className="pt-[3px]">
          <IconContainer status={status} slugName={slug} index={index} />
        </div>
        {!isLastIndex ? (
          <div
            className={`w-full ${
              status === "completed" ? "h-1/2" : "h-3/4"
            } border-white border-l absolute ${
              status === "completed" ? "top-[32px]" : "top-[23px]"
            } ml-[7px]`}
          />
        ) : null}
        <div
          className={`text-white  pr-4${
            isActive ? " opacity-100 " : " opacity-50 "
          } flex flex-column pb-[10px] max-[80%] flex-wrap cursor-pointer`}
          onClick={() => {
            if (!isActive) handleFormSubmit(null);
            setIsDialogOpen(false);

            router.push(`/form?${updatedQueryString}`);
          }}
        >
          {title}
        </div>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild className="cursor-pointer">
              {error?.some(
                (item: any) =>
                  item?.displayName === title && item?.missingFieldsExist
              ) && (
                <div className="w-6 ">
                  <AlertCircle
                    name="shield-alert"
                    height={20}
                    width={20}
                    color={"var(--color-surface)"}
                  />
                </div>
              )}
            </TooltipTrigger>
            <TooltipContent className="bg-white">
              <p>
                {`${
                  error?.find((item: any) => item?.displayName === title)
                    ?.missingFieldsCount
                } fields  is required`}
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
};

interface IconContainerProps {
  status: string;
  slugName: string | any;
  index: number;
}

const IconContainer = ({ slugName, status, index }: IconContainerProps) => {
  const pathname = usePathname();
  const searchParams: any = new URLSearchParams(window.location.search);
  const currentRoute = pathname.replace("/", "");
  const step = searchParams.get("step");
  if (step == index) {
    // current section big dot
    return (
      <div className="w-6">
        <div className="h-4 w-4 bg-white rounded-full" />
      </div>
    );
  }
  if (currentRoute !== slugName && status !== "completed") {
    // not current section
    return (
      <div className="w-6">
        <div className="h-[10px] w-[10px] bg-white rounded-full opacity-50 ml-[2px]" />
      </div>
    );
  }
  if (status === "completed") {
    // tick mark completed
    return (
      <div className="w-6">
        <Check name="Check" height={20} width={20} color={"#FFFFFF"} />
      </div>
    );
  }
};
