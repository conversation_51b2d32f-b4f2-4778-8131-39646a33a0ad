"use client";
import React, { useEffect } from "react";
import { LayoutWrapper } from "../custom/wrapper";
import Image from "next/image";
import { useAtom } from "jotai";
import {
  applicationId,
  consumerAPIKey,
  email,
  nextForm,
  programmeName,
  staticContentsAtom,
} from "@/lib/atom";
import { Button } from "../ui/button";
import { Check } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getOapForm, getOapFormSections, getStudentDetails } from "@/api/api";
import loader from "../../public/loader.svg";
import { LinkRenderer } from "../custom/linkRender";
import ReactMarkdown from "react-markdown";
import { useRouter } from "next/navigation";
import { useFormContext } from "react-hook-form";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";

const ThanksComponent = () => {
  const [programme] = useAtom(programmeName);
  const router = useRouter();
  const [nextFormDetails]: any = useAtom(nextForm);
  const [apiKey] = useAtom(consumerAPIKey);

  const { reset } = useFormContext();
  const [userEmail] = useAtom(email);
  const [application] = useAtom(applicationId);

  const {
    data: studentDetails,
    refetch: refetchStudent,
    isFetching: studentDetailsIsFetching,
  } = useQuery({
    queryKey: [`${userEmail}-form`],
    queryFn: async () => {
      if (userEmail) {
        let res: any = await getStudentDetails(
          nextFormDetails?.oap,
          userEmail,
          application,
          apiKey
        );

        return res;
      } else {
        return;
      }
    },
    enabled: !!(nextFormDetails && userEmail && application),
  });

  const checkVisibility = (visibleWhen: any) => {
    if (!visibleWhen) {
      return true;
    }

    const fieldValue = studentDetails?.[visibleWhen?.fieldName];

    if (visibleWhen.condition === "notEqual") {
      return !visibleWhen.value.some(
        (entry: any) => entry.value === fieldValue
      );
    }

    if (Array.isArray(visibleWhen.value)) {
      return visibleWhen.value.includes(fieldValue);
    }
    return visibleWhen.value === fieldValue;
  };

  useEffect(() => {
    window.addEventListener("popstate", () => {
      window.history.replaceState(null, "", "/application");
      router.replace("/application");
      reset();
    });
  }, [router]);

  const { data: sectionQuery, isFetching: sectionQueryFetching } = useQuery({
    queryKey: [`${nextFormDetails?.oap}-${nextFormDetails?.mode}-form`],
    queryFn: async () => {
      let res = await getOapForm(
        {
          oap: nextFormDetails?.oap,
          form:
            nextFormDetails?.type === "single"
              ? nextFormDetails?.form
              : nextFormDetails?.[nextFormDetails?.currentOap],
          mode: nextFormDetails?.mode,
        },
        apiKey
      );
      return res;
    },
    enabled: true,
  });

  const {
    data: thankYouContent,
    isFetching: thankYouContentFetching,
    isError,
  } = useQuery({
    queryKey: [`${nextFormDetails?.oap}-${nextFormDetails?.mode}-thankyou`],
    queryFn: async () => {
      let res = await getOapFormSections(
        {
          oap: nextFormDetails?.oap,
          mode: nextFormDetails?.mode,
          formName: `${nextFormDetails?.oap}_${nextFormDetails?.mode}`,
          sectionName: "THANK_YOU",
        },
        apiKey
      );
      return res;
    },
    enabled: true,
  });

  return (
    <>
      <div className=" w-full ">
        <header className=" p-2 h-[98px] md:p-6 header bg-on-background flex gap-4 w-full items-center">
          <>
            <a className="header-brand-link max-h-[98px] max-w-[150px] ">
              <div className=" max-h-[98px] max-w-[120px] w-[120px] items-center">
                {sectionQuery?.logoInfo?.signedUrl ? (
                  <Image
                    className="header -brand-image  sm:hidden md:flex lg:flex xl:flex"
                    src={sectionQuery?.logoInfo?.signedUrl}
                    alt="ibat_logo_int"
                    style={{ objectFit: "contain" }}
                    width={sectionQuery?.logoInfo?.width ?? 32}
                    height={sectionQuery?.logoInfo?.height}
                  />
                ) : (
                  <div className="flex justify-center items-center max-h-[98px] h-full max-w-[102px] w-[102px] ">
                    <Image
                      className="header -brand-image sm:hidden md:flex lg:flex xl:flex "
                      src={loader}
                      alt="ibat_logo_int"
                      style={{ objectFit: "contain" }}
                      height={12}
                      width={32}
                    />
                  </div>
                )}
              </div>
            </a>
          </>
          <div className="header-content text-highlight text-xl font-normal items-center  sm:flex hidden ">
            <div className="header-title header-title_general border-l border-[#] pl-[34px]">
              <div className="header-title-value">{"Application Portal"}</div>
            </div>

            <div className="header-profile">
              <div
                className="header-profile-user-languages"
                id="userLanguages"
              ></div>
            </div>
          </div>
          <div className="header-content header-content_mini">
            <div className="header-profile">
              <div
                className="header-profile-user-languages"
                id="userLanguages"
              ></div>
            </div>
          </div>
        </header>
        <div className=" lg:px-12 md:mt-5 py-12 ">
          {thankYouContentFetching ? (
            <div
              className="flex justify-center items-center"
              style={{ height: 150 }}
            >
              <Image
                priority
                src={loader}
                height={32}
                width={32}
                alt="Follow us on Twitter"
              />
            </div>
          ) : thankYouContent && thankYouContent?.markdownText ? (
            <div className="prose dark:prose-invert max-w-none mb-6 text-sm px-20">
              <ReactMarkdown
                className="markDown my-4"
                components={{ a: LinkRenderer }}
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeRaw]}
              >
                {thankYouContent?.markdownText}
              </ReactMarkdown>
            </div>
          ) : (
            <LayoutWrapper>
              <div className="index-content-header mb-8 sm:mb-8 ">
                <div className="index-content-heading text-3xl font-bold">
                  {"Thank you for applying"}
                </div>
              </div>
              <div className="index-content-container text-[14px] sm:text-base mt-2">
                <div className="index-content-text font-bold  ">
                  <p>{"Your application has been successfully submitted!"}</p>
                </div>
                <div className="index-content-text mt-2 font-thin ">
                  <p>
                    {
                      "You will be receiving a follow up e-mail from the Admissions Office regarding your application. If you didn't receive it, please check your Spam folder"
                    }
                  </p>
                </div>
              </div>
            </LayoutWrapper>
          )}
          <LayoutWrapper>
            <div className="index-widget shadow-[0_3px_10px_rgb(0,0,0,0.2)] rounded p-5 sm:p-[30px] bg-gray-200 ">
              {sectionQueryFetching || thankYouContentFetching ? (
                <div
                  className="flex justify-center items-center"
                  style={{ height: 150 }}
                >
                  <Image
                    priority
                    src={loader}
                    height={32}
                    width={32}
                    alt="Follow us on Twitter"
                  />
                </div>
              ) : thankYouContent && thankYouContent?.customReport ? (
                <div className="index-widget-wrapper">
                  <form className="form" noValidate>
                    <div className="form-section w-full flex flex-col-reverse sm:flex-row gap-4  ">
                      <h3 className="form-section-title text-[#1c6a8f] font-semibold mb-2 text-xl"></h3>
                      <div className=" w-full flex flex-col lg:flex-row">
                        <div className="form-section-content justify-start lg:justify-start flex flex-wrap gap-4  font-thin w-full sm:w-2/6 md:w-full lg:w-2/6 sm:mb-4 md:mb-4 mb-4">
                          <div className=" font-bold  text-lg ">
                            {programme}
                          </div>
                        </div>
                        <div className="form-action w-full flex-col flex sm:flex-row lg:flex-row md:flex-row lg:px-4 text-sm sm:gap-10">
                          {sectionQuery?.section?.length > 0 && (
                            <>
                              <div className="flex-shrink-0 flex flex-col ">
                                {sectionQuery?.section
                                  .slice(
                                    0,
                                    Math.ceil(sectionQuery?.section?.length / 2)
                                  )
                                  .map((item: any, i: number) => (
                                    <div key={i} className="flex">
                                      <Check
                                        name="Check"
                                        height={15}
                                        width={15}
                                        className="font-bold m-1"
                                      />
                                      <div>{item?.displayName}</div>
                                    </div>
                                  ))}
                              </div>

                              <div className="w-full  flex flex-col">
                                {sectionQuery?.section
                                  .slice(
                                    Math.ceil(sectionQuery.section.length / 2)
                                  )
                                  .map((item: any, i: number) => (
                                    <div
                                      key={
                                        i +
                                        Math.ceil(
                                          sectionQuery.section.length / 2
                                        )
                                      }
                                      className="flex"
                                    >
                                      <Check
                                        name="Check"
                                        height={15}
                                        width={15}
                                        className="font-bold m-1"
                                      />
                                      <div>{item?.displayName}</div>
                                    </div>
                                  ))}
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                      <div className="flex">
                        <Button className="text-xs sm:text-sm  text-background rounded font-bold  w-[42%] sm:w-[160px] ">
                          100%
                          <span className=" ml-1 opacity-50 ">COMPLETED</span>
                        </Button>
                      </div>
                    </div>
                  </form>
                </div>
              ) : (
                <div className="index-widget-wrapper">
                  <form className="form" noValidate>
                    <div className="form-section w-full flex flex-col-reverse sm:flex-row sm:items-center gap-4 ">
                      <h3 className="form-section-title text-[#1c6a8f] font-semibold mb-2 text-xl"></h3>
                      <div className=" w-full flex flex-col lg:flex-row">
                        <div className="form-section-content justify-start lg:justify-center flex flex-wrap gap-4 items-center font-thin w-full sm:w-2/6 md:w-full lg:w-2/6 sm:mb-4 md:mb-4 mb-4">
                          <div className=" font-bold  text-lg ">
                            {programme}
                          </div>
                        </div>
                        <div className="form-action w-full flex-col flex-wrap gap-2 justify-start lg:px-4  text-sm">
                          {sectionQuery?.section?.length > 0 &&
                            sectionQuery?.section?.map(
                              (item: any, i: number) =>
                                checkVisibility(item?.visibleWhen) && (
                                  <div
                                    key={i}
                                    className="w-full lg:w-1/2   mx-auto flex justify-start"
                                  >
                                    <Check
                                      name="Check"
                                      height={15}
                                      width={15}
                                      className=" font-bold m-1 "
                                    />
                                    <div key={i}>{item?.displayName}</div>
                                  </div>
                                )
                            )}
                        </div>
                      </div>
                      <Button className=" text-xs sm:text-sm  text-background rounded font-bold  w-[42%] sm:w-[160px] ">
                        100%<span className=" ml-1 opacity-50 ">COMPLETED</span>
                      </Button>
                    </div>
                  </form>
                </div>
              )}
            </div>
          </LayoutWrapper>
        </div>
      </div>
    </>
  );
};

export default ThanksComponent;
