"use client";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { ProgressProvider } from "@/lib/progress-context";
import { FormProvider, useForm } from "react-hook-form";

export default function Providers({ children }: any) {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
      },
    },
  });
  const methods = useForm();

  return (
    <QueryClientProvider client={queryClient}>
      <ProgressProvider progress={0}>
        <FormProvider {...methods}>{children}</FormProvider>
      </ProgressProvider>
    </QueryClientProvider>
  );
}
