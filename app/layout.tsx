import type { Metadata } from "next";
import "./globals.css";
import { injectThemeStyle } from "@/helpers/ThemeStyle";
import { getOapDetail } from "@/api/api";
import Providers from "./provider";
import { SSRInjectStyles } from "@/helpers/SSRInjectStyles";
import { Toaster } from "react-hot-toast";
import { azoSans } from "./fonts";

export const metadata: Metadata = {
  title: process.env.NEXT_PUBLIC_OAP_TITLE,
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const res = await getOapDetail({
    name: process.env.NEXT_PUBLIC_OAP_NAME,
    mode: process.env.NEXT_PUBLIC_OAP_MODE,
  });

  const themeStyle = {
    "--body-font-family": res?.theme?.["font-family"]?.name,
    ...(res?.theme?.colors || {}),
  };

  injectThemeStyle(themeStyle);

  return (
    <html lang="en">
      <head>
        <SSRInjectStyles />
        <link
          rel="icon"
          href={`/${process.env.NEXT_PUBLIC_OAP_TITLE?.toLowerCase()}_logo.svg`.replace(
            " ",
            ""
          )}
          sizes="any"
        />
      </head>
      <body className={azoSans.variable}>
        <Providers>{children}</Providers>
        <Toaster
          position="top-center"
          toastOptions={{
            custom: {
              duration: 2000,
            },
          }}
        />
      </body>
    </html>
  );
}
