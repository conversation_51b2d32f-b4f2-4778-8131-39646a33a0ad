import type { Metadata } from "next";
import "./globals.css";
import { injectThemeStyle } from "@/helpers/ThemeStyle";
import { getOapDetail } from "@/api/api";
import Providers from "./provider";
import { SSRInjectStyles } from "@/helpers/SSRInjectStyles";
import { Toaster } from "react-hot-toast";
import { azoSans } from "./fonts";

export const metadata: Metadata = {
  title: process.env.NEXT_PUBLIC_OAP_TITLE,
  icons: {
    icon: `/${process.env.NEXT_PUBLIC_OAP_TITLE?.toLowerCase()}_logo.svg`.replace(
      " ",
      ""
    ),
    apple:
      `/${process.env.NEXT_PUBLIC_OAP_TITLE?.toLowerCase()}_logo.svg`.replace(
        " ",
        ""
      ),
  },
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const res = await getOapDetail({
    name: process.env.NEXT_PUBLIC_OAP_NAME,
    mode: process.env.NEXT_PUBLIC_OAP_MODE,
  });

  const themeStyle = {
    "--body-font-family": res?.theme?.["font-family"]?.name,
    ...(res?.theme?.colors || {}),
  };

  injectThemeStyle(themeStyle);

  return (
    <html lang="en">
      <head>
        <SSRInjectStyles />
        <link
          rel="icon"
          href={`/${process.env.NEXT_PUBLIC_OAP_TITLE?.toLowerCase()}_logo.svg`.replace(
            " ",
            ""
          )}
          type="image/svg+xml"
        />
        <link
          rel="alternate icon"
          href={`/${process.env.NEXT_PUBLIC_OAP_TITLE?.toLowerCase()}_logo.svg`.replace(
            " ",
            ""
          )}
          type="image/svg+xml"
        />
        <link
          rel="apple-touch-icon"
          href={`/${process.env.NEXT_PUBLIC_OAP_TITLE?.toLowerCase()}_logo.svg`.replace(
            " ",
            ""
          )}
        />
      </head>
      <body className={azoSans.variable}>
        <Providers>{children}</Providers>
        <Toaster
          position="top-center"
          toastOptions={{
            custom: {
              duration: 2000,
            },
          }}
        />
      </body>
    </html>
  );
}
