"use client";

import {
  getOapDetail,
  getOapForm,
  getOapFormSections,
  saveOapForm,
  getLookUpData,
} from "@/api/api";
import { FormLayout } from "@/components/custom/formLayout";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import {
  applicationId,
  consumerAPIKey,
  email,
  nextForm,
  preferredLanguage,
  selectedCountry,
} from "@/lib/atom";
import { useAtom } from "jotai";
import CountrySelector from "@/components/custom/location-card";
import { useRouter } from "next/navigation";
import loader from "../../public/loader.svg";
import Image from "next/image";
import { useLookUpData } from "@/hooks/useLookupData";

export default function Page() {
  const [nextFormDetails, setNextFormDetails]: any = useAtom(nextForm);
  const [preferLang, setPreferLang] = useAtom(preferredLanguage);
  const router = useRouter();
  const methods = useFormContext();
  const [, setApplicationId] = useAtom(applicationId);
  const [userEmail, setUserEmail] = useAtom(email);
  const [pageDetails, setPageDetails] = useState({
    screen: process.env.NEXT_PUBLIC_OAP_NAME,
    mode: process.env.NEXT_PUBLIC_OAP_MODE,
  });

  const [_, setSelectedCountry] = useAtom(selectedCountry);

  const [apiKey] = useAtom(consumerAPIKey);

  const { data: locationOptions ,refetch: refetchLocationOptions } = useQuery({
    queryKey: ["oap/picklist/locationOptions?brand=UEG"],
    queryFn: async () => {
      let res = await getLookUpData(
        {
          name: "oap/picklist/locationOptions?brand=UEG",
        },
        apiKey,
        {...(preferLang && { displayLanguage: preferLang })},
      );
      return res;
    },
    enabled: !!apiKey,
  });

  useEffect(() => {
    refetchLocationOptions();
  }, [preferLang]);
  const {
    data: pageQuery,
    isFetching: isPageFetching,
    refetch: refetchPageQuery,
  } = useQuery({
    queryKey: [`${pageDetails.screen}-${pageDetails.mode}`],
    queryFn: async () => {
      let res = await getOapDetail(
        {
          name: pageDetails.screen,
          mode: pageDetails.mode,
          ...(preferLang === "de" && { language: "de" }),
        },
        apiKey
      );
      return res;
    },
    enabled: true,
  });

  const {
    data: sectionQuery,
    isFetching: sectionIsFetching,
    refetch: refetchSectionQuery,
  } = useQuery({
    queryKey: [
      `${nextFormDetails?.oap}-${nextFormDetails?.mode}-${nextFormDetails?.form}`,
    ],
    queryFn: async () => {
      if (
        nextFormDetails?.form &&
        nextFormDetails?.oap &&
        nextFormDetails?.mode
      ) {
        let res = await getOapForm(
          {
            oap: nextFormDetails?.oap,
            form: nextFormDetails?.form,
            mode: nextFormDetails?.mode,
            ...(preferLang === "de" && { language: "de" }),
          },
          apiKey
        );
        return res;
      } else {
        // router.push("/login");
        return;
      }
    },
    enabled: !!nextFormDetails,
  });
  const {
    data: formQuery,
    isFetching: formQueryIsFetching,
    refetch: refetchFormQuery,
  } = useQuery({
    // next sections page
    queryKey: [
      `${nextFormDetails?.oap}-
        ${nextFormDetails?.mode}-${sectionQuery?.form}`,
    ],
    queryFn: async () => {
      if (!(sectionQuery?.section?.[0]?.name && sectionQuery?.form)) return;

      let res = await getOapFormSections(
        {
          oap: nextFormDetails?.oap,
          mode: nextFormDetails?.mode,
          formName: sectionQuery?.form,
          sectionName: sectionQuery?.section?.[0]?.name,
          ...(preferLang === "de" && { language: "de" }),
        },
        apiKey
      );
      return res;
    },
    enabled: !!(nextFormDetails && sectionQuery && pageQuery),
  });

  const getNextForm = () => {
    const formDetail = formQuery?.fieldData?.find(
      (item: any) => item?.type === "button"
    )?.nextForm;

    return formDetail;
  };

  const handleSelect = async (value: any) => {
    const fieldDisplayName = formQuery?.fieldData?.[0]?.fieldDisplayName;
    const fieldName = formQuery?.fieldData?.[0]?.fieldName;
    setSelectedCountry(value);
    const basicDetails = JSON.parse(
      localStorage.getItem("basic-details") ?? "[]"
    );

    let basePayload = {
      email: userEmail,
      applicationStatus: "inProgress",
      ...basicDetails,
      sectionLabel: formQuery?.label,
    };

    if (value) {
      basePayload = {
        ...basePayload,
        // [fieldDisplayName]: label,
        [fieldName]: value,
      };
      methods.setValue(fieldName,value)
      if(fieldDisplayName){
        methods.setValue(fieldDisplayName,value)
      }
    }

    const res = await saveOapForm(
      basePayload,
      { oapName: nextFormDetails?.oap, mode: nextFormDetails?.mode },
      apiKey
    );

    if (res?.email && res?.applicationId) {
      let nextForm = await getNextForm();
      let nextApply = basicDetails?.[nextForm?.dependentField];
      setNextFormDetails(getNextForm());
      setUserEmail(res?.email);
      setApplicationId(res?.applicationId);

      router.push(`/application-filter`);
    }
  };

  useEffect(() => {
    refetchPageQuery();
    refetchSectionQuery();
    refetchFormQuery();
  }, [preferLang]);

  if (sectionIsFetching || formQueryIsFetching || isPageFetching) {
    return (
      <main className="min-h-screen bg-on-background flex flex-col items-center justify-center overflow-scroll w-full">
        <Image
          priority
          src={loader}
          height={32}
          width={32}
          alt="Follow us on Twitter"
        />
      </main>
    );
  }

  return (
    <FormLayout pageQuery={pageQuery}>
      <CountrySelector
        displayName={formQuery?.fieldData[0]?.displayName}
        options={locationOptions || formQuery?.fieldData[0]?.pickListValues}
        required={false}
        imgSrc={formQuery?.fieldData[0]?.cardBgg}
        formQuery={formQuery}
        handleSelect={handleSelect}
      />
    </FormLayout>
  );
}
