"use client";

import { useParams } from "next/navigation";
import GenericRequestForm from "@/components/Forms/genericRequestForm";
import { requestTypeDataAtom } from "@/lib/atom";
import { useAtom } from "jotai";
import { Alert<PERSON>riangle, Loader2 } from "lucide-react";

export default function RequestTypePage() {
  const params = useParams();
  const requestType = params.requestType as string;
  const [requestTypeData] = useAtom(requestTypeData<PERSON>tom);

  if (Object.keys(requestTypeData).length === 0) {
    return (
      <div className="flex w-full bg-background min-h-screen items-center justify-center p-4">
        <div
          id="request-types-not-found"
          className="bg-background border border-gray-200 rounded-2xl shadow-xl p-8 max-w-md text-center"
        >
          <div className="w-20 h-20 rounded-full bg-gradient-to-br from-orange-100 to-orange-50 flex items-center justify-center mx-auto mb-6 shadow-inner">
            <Loader2 className="h-10 w-10 text-primary animate-spin drop-shadow-sm" />
          </div>
          <h1 className="text-xl font-bold mb-3 text-gray-800">
            Opening a selected form
          </h1>
          <p className="text-gray-600 mb-6 leading-relaxed">
            Please wait while we prepare your form...
          </p>
        </div>
      </div>
    );
  }

  // Find the request type configuration
  const requestConfig = requestTypeData?.find(
    (type: any) => type.path === `/requests/${requestType}`
  );

  if (!requestConfig) {
    return (
      <div className="flex w-full bg-background min-h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Request Type Not Found</h1>
          <p className="text-gray-600">
            The requested form type could not be found.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex w-full bg-background min-h-screen">
      <GenericRequestForm requestConfig={requestConfig} />
    </div>
  );
}
