"use client";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function Page() {
  const router = useRouter();

  useEffect(() => {
    const redirectToApplication = () => {
      router.replace("/application");
    };

    redirectToApplication();
  }, []);

  return (
    <main className="min-h-screen bg-primary flex flex-col items-center justify-center overflow-scroll">
      {/* <Home /> */}
    </main>
  );
}
