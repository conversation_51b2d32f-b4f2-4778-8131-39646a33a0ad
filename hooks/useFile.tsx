import { deleteDocument, getPreSignedURL, getUpdatedDoc } from "@/api/api";
import { consumerAPIKey } from "@/lib/atom";
import { useAtom } from "jotai";

export const useFile = () => {
  const [apiKey] = useAtom(consumerAPIKey);
  const uploadFile = async ({
    payload,
    email,
    studentDetail,
    studentDocuments,
    watch = () => {},
    applicationId,
    oapName,
    businessUnitFilter,
  }: {
    payload: Object;
    email: any;
    applicationId: string;
    oapName: string;
    studentDetail?: any;
    watch?: Function;
    studentDocuments?: any;
    businessUnitFilter?: string;
  }) => {
    try {
      const newPayload: any = {
        ...payload,
        email,
        applicationId,
        oapName,
        businessUnitFilter,
        //@ts-ignore
        documentName: payload?.path,
      };

      const { path, file, pathName, ...rest } = newPayload;

      const result = await getPreSignedURL(
        {
          contentType: newPayload.type,
          // path: `${newPayload.email}/${newPayload.documentType}/${newPayload.path}`,
          ...rest,
        },
        apiKey
      );

      let presignedUrlResponse: any;
      if (result?.signedUrl) {
        presignedUrlResponse = await fetch(result?.signedUrl, {
          method: "PUT",
          headers: {
            "Content-Type": newPayload.contentType,
          },
          body: newPayload?.file,
        });
      }
      const uploadDocResponse = await getUpdatedDoc(
        {
          contentType: newPayload.type,
          // path: `${newPayload.email}/${newPayload.documentType}/${newPayload.path}`,
          ...rest,
        },
        apiKey
      );
      // let response;
      // if (presignedUrlResponse?.status === 200) {
      //   response = await saveUploadedFile({
      //     path: newPayload.path,
      //     pathName: newPayload.pathName,
      //     s3FilePath: `${newPayload.email}/${newPayload.documentType}/${newPayload.path}`,
      //     email: newPayload.email,
      //     fileType: newPayload.fileType,
      //   });
      // }

      // const response = await saveUploadedFile(newPayload);
      // if (response?.message) {
      //   return toast({
      //     description: response?.message,
      //   });
      // }
      // const newMetaData = handleMetaData({
      //   metaData: metaData,
      //   studentDetail,
      // });
      // const percentage = handleProgressCalculation({
      //   studentDetail,
      //   metaData: newMetaData,
      //   watch,
      //   documents: studentDocuments,
      // });
      // const updatePayload = {
      //   progressPercentage: percentage,
      //   email: email || studentDetail.email,
      // };
      // if (studentDetail?.email) {
      //   await updateStudentDetail(updatePayload);
      // } else {
      //   await createStudentDetail(updatePayload);
      // }
      // const documents = await getUploadedFile(email);
      // const filteredDocuments = documents?.filter(
      //   (file: any) => file?.fileType === newPayload?.fileType
      // );

      return {
        ...newPayload,
        ocrFields: newPayload.processOcr ? uploadDocResponse : {},
        successOcrFields: newPayload.processOcr && !uploadDocResponse.message,
        path,
        file,
        pathName,
        status: true,
      };
    } catch (error: any) {
      console.log({ error });
      return { message: error?.message, status: false };
    }
  };

  const deleteFile = async ({
    payload,
    studentDetail,
    watch = () => {},
  }: {
    payload: any;
    studentDetail?: any;
    watch?: any;
  }) => {
    try {
      const response = await deleteDocument(studentDetail, apiKey);

      //  deleteUploadFile(payload);
      // if (response?.message) {
      //   return toast({
      //     description: response?.message,
      //   });
      // }
      // const newMetaData = handleMetaData({ metaData, studentDetail });
      // const percentage = handleProgressCalculation({
      //   studentDetail,
      //   metaData: newMetaData,
      //   watch,
      // });
      // const updatePayload = {
      //   progressPercentage: percentage,
      //   email: studentDetail.email,
      // };
      // await updateStudentDetail(updatePayload);
      // return { progressPercentage: percentage };
    } catch (error) {
      console.log({ error });
    }
  };
  return { uploadFile, deleteFile };
};
