import { getLookUpData } from "@/api/api";
import { consumerAPIKey } from "@/lib/atom";
import { useQueryClient } from "@tanstack/react-query";
import { useAtom } from "jotai";
interface lookupDataProps {
  queryKey?: string;
  name?: string;
  preferredLanguage?: string;
}

export const useLookUpData = () => {
  const queryClient = useQueryClient();
  const [apiKey] = useAtom(consumerAPIKey);

  const fetchLookUpData = async ({
    queryKey,
    name,
    preferredLanguage,
  }: lookupDataProps) => {
    const result = await getLookUpData({ name: name }, apiKey, {
      ...(preferredLanguage && { displayLanguage: preferredLanguage }),
    });
    queryClient.setQueryData([queryKey], result);
    return result;
  };

  return { data: fetchLookUpData };
};
